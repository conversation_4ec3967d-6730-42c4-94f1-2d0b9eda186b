
<svg width="1000" height="700" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="1000" height="700" fill="#FFFFFF"/>
  <!-- Input Processing Stage -->
  <rect x="50" y="40" width="900" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
  <text x="80" y="70" font-size="20" font-weight="bold" fill="#0D47A1">INPUT PROCESSING and CONTEXT INTEGRATION</text>
  <!-- User Query -->
  <rect x="80" y="90" width="140" height="60" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="8"/>
  <text x="150" y="115" text-anchor="middle" font-size="16" font-weight="bold">User Query</text>
  <text x="150" y="135" text-anchor="middle" font-size="14">Text input</text>
  <!-- RAG Context -->
  <rect x="240" y="90" width="180" height="60" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="8"/>
  <text x="330" y="115" text-anchor="middle" font-size="16" font-weight="bold">RAG Context</text>
  <text x="330" y="135" text-anchor="middle" font-size="14">Retrieved documents</text>
  <!-- Context Engine -->
  <rect x="440" y="90" width="200" height="60" fill="#90caf9" stroke="#1976d2" stroke-width="2" rx="8"/>
  <text x="540" y="115" text-anchor="middle" font-size="16" font-weight="bold">Context Engine</text>
  <text x="540" y="135" text-anchor="middle" font-size="14">Prompt assembly and optimization</text>
  <!-- Tokenization -->
  <rect x="660" y="90" width="140" height="60" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="8"/>
  <text x="730" y="115" text-anchor="middle" font-size="16" font-weight="bold">Tokenization</text>
  <text x="730" y="135" text-anchor="middle" font-size="14">Text → Token IDs</text>
  <!-- Context Buffer -->
  <rect x="820" y="90" width="110" height="60" fill="#64b5f6" stroke="#1976d2" stroke-width="2" rx="8"/>
  <text x="875" y="115" text-anchor="middle" font-size="16" font-weight="bold">Context Buffer</text>
  <text x="875" y="135" text-anchor="middle" font-size="14">Token storage</text>
  <!-- Model Core Stage -->
  <rect x="50" y="190" width="900" height="300" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
  <text x="80" y="220" font-size="20" font-weight="bold" fill="#E65100">LLaMA.rn MODEL CORE</text>
  <!-- Model Weights (Large Section) -->
  <rect x="80" y="240" width="300" height="180" fill="#ffcc02" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="230" y="270" text-anchor="middle" font-size="18" font-weight="bold">Model Weights (GGUF)</text>
  <!-- Weight Details -->
  <rect x="100" y="290" width="120" height="60" fill="#ffe0b2" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="160" y="315" text-anchor="middle" font-size="14" font-weight="bold">Transformer Layers</text>
  <text x="160" y="335" text-anchor="middle" font-size="12">Attention • FFN weights</text>
  <rect x="240" y="290" width="120" height="60" fill="#ffe0b2" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="300" y="315" text-anchor="middle" font-size="14" font-weight="bold">Embeddings</text>
  <text x="300" y="335" text-anchor="middle" font-size="12">Token → Vector</text>
  <rect x="100" y="360" width="260" height="50" fill="#ffb74d" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="230" y="380" text-anchor="middle" font-size="14" font-weight="bold">Quantized Storage</text>
  <text x="230" y="400" text-anchor="middle" font-size="12">Memory-mapped • 1B: ~1.2GB • 3B: ~3.5GB</text>
  <!-- Forward Pass Pipeline -->
  <rect x="400" y="240" width="200" height="180" fill="#ffcc02" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="500" y="270" text-anchor="middle" font-size="18" font-weight="bold">Forward Pass Pipeline</text>
  <!-- Forward Pass Steps -->
  <rect x="420" y="290" width="160" height="35" fill="#ffe0b2" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="500" y="312" text-anchor="middle" font-size="14" font-weight="bold">1. Embedding Lookup</text>
  <rect x="420" y="335" width="160" height="35" fill="#ffe0b2" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="500" y="357" text-anchor="middle" font-size="14" font-weight="bold">2. Attention Computation</text>
  <rect x="420" y="375" width="160" height="35" fill="#ffe0b2" stroke="#f57c00" stroke-width="1" rx="5"/>
  <text x="500" y="397" text-anchor="middle" font-size="14" font-weight="bold">3. Next Token Prediction</text>
  <!-- KV Cache -->
  <rect x="620" y="240" width="160" height="120" fill="#ffcc02" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="700" y="270" text-anchor="middle" font-size="18" font-weight="bold">KV Cache</text>
  <text x="700" y="295" text-anchor="middle" font-size="14">Attention States</text>
  <text x="700" y="315" text-anchor="middle" font-size="12">Keys • Values</text>
  <text x="700" y="330" text-anchor="middle" font-size="12">Per layer cache</text>
  <text x="700" y="345" text-anchor="middle" font-size="12">~50-200MB total</text>
  <!-- Generation Buffers -->
  <rect x="800" y="240" width="130" height="120" fill="#ffcc02" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="865" y="270" text-anchor="middle" font-size="16" font-weight="bold">Generation</text>
  <text x="865" y="290" text-anchor="middle" font-size="16" font-weight="bold">Buffers</text>
  <text x="865" y="315" text-anchor="middle" font-size="12">Logits computation</text>
  <text x="865" y="330" text-anchor="middle" font-size="12">Sampling buffers</text>
  <text x="865" y="345" text-anchor="middle" font-size="12">~20-50MB</text>
  <!-- Memory Management -->
  <rect x="620" y="370" width="310" height="110" fill="#ffb74d" stroke="#f57c00" stroke-width="1" rx="8"/>
  <text x="775" y="395" text-anchor="middle" font-size="16" font-weight="bold">Mobile Memory Management</text>
  <text x="775" y="415" text-anchor="middle" font-size="13">Dynamic context window scaling (2k→128k tokens)</text>
  <text x="775" y="435" text-anchor="middle" font-size="13">KV cache compression under memory pressure</text>
  <text x="775" y="455" text-anchor="middle" font-size="13">Attention state pruning for long contexts</text>
  <text x="775" y="470" text-anchor="middle" font-size="13">Model quantization (Q4_0, Q5_0, Q8_0)</text>
  <!-- Generation Pipeline Stage -->
  <rect x="50" y="520" width="900" height="140" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="10"/>
  <text x="80" y="550" font-size="20" font-weight="bold" fill="#2E7D32">GENERATION PIPELINE</text>
  <!-- Token Generation -->
  <rect x="80" y="570" width="150" height="70" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="8"/>
  <text x="155" y="595" text-anchor="middle" font-size="16" font-weight="bold">Token Generation</text>
  <text x="155" y="615" text-anchor="middle" font-size="13">Logits → Probabilities</text>
  <text x="155" y="630" text-anchor="middle" font-size="13">Sampling strategies</text>
  <!-- Streaming -->
  <rect x="250" y="570" width="150" height="70" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="8"/>
  <text x="325" y="595" text-anchor="middle" font-size="16" font-weight="bold">Token Streaming</text>
  <text x="325" y="615" text-anchor="middle" font-size="13">Real-time output</text>
  <text x="325" y="630" text-anchor="middle" font-size="13">UI responsiveness</text>
  <!-- Performance Monitoring -->
  <rect x="420" y="570" width="180" height="70" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="8"/>
  <text x="510" y="595" text-anchor="middle" font-size="16" font-weight="bold">Performance Monitoring</text>
  <text x="510" y="615" text-anchor="middle" font-size="13">Tokens/sec • TTFT tracking</text>
  <text x="510" y="630" text-anchor="middle" font-size="13">Memory usage alerts</text>
  <!-- Stop Conditions -->
  <rect x="620" y="570" width="150" height="70" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="8"/>
  <text x="695" y="595" text-anchor="middle" font-size="16" font-weight="bold">Stop Conditions</text>
  <text x="695" y="615" text-anchor="middle" font-size="13">EOS detection</text>
  <text x="695" y="630" text-anchor="middle" font-size="13">Length limits</text>
  <!-- Output Processing -->
  <rect x="790" y="570" width="150" height="70" fill="#a5d6a7" stroke="#388e3c" stroke-width="2" rx="8"/>
  <text x="865" y="595" text-anchor="middle" font-size="16" font-weight="bold">Output Processing</text>
  <text x="865" y="615" text-anchor="middle" font-size="13">Token → Text</text>
  <text x="865" y="630" text-anchor="middle" font-size="13">Response delivery</text>
  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2" />
    </marker>
    <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f57c00" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#388e3c" />
    </marker>
  </defs>
  <!-- Input flow -->
  <path d="M 220 120 L 240 120" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
  <path d="M 420 120 L 440 120" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
  <path d="M 640 120 L 660 120" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
  <path d="M 800 120 L 820 120" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
  <!-- To model core -->
  <path d="M 540 150 L 540 190" stroke="#1976d2" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
  <path d="M 875 150 L 875 190" stroke="#1976d2" stroke-width="2" marker-end="url(#arrowhead-blue)"/>
  <!-- Model processing flow -->
  <path d="M 380 330 L 400 330" stroke="#f57c00" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
  <path d="M 600 300 L 620 300" stroke="#f57c00" stroke-width="2" marker-end="url(#arrowhead-orange)"/>
  <path d="M 700 360 L 700 370" stroke="#f57c00" stroke-width="1" marker-end="url(#arrowhead-orange)"/>
  <!-- To generation -->
  <path d="M 500 490 L 500 520" stroke="#f57c00" stroke-width="3" marker-end="url(#arrowhead-orange)"/>
  <!-- Generation flow -->
  <path d="M 230 605 L 250 605" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowhead-green)"/>
  <path d="M 400 605 L 420 605" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowhead-green)"/>
  <path d="M 600 605 L 620 605" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowhead-green)"/>
  <path d="M 770 605 L 790 605" stroke="#388e3c" stroke-width="2" marker-end="url(#arrowhead-green)"/>
  <!-- Memory annotations -->
<text x="950" y="280" font-size="12" font-weight="bold" fill="#f57c00" text-anchor="end">70-90%</text>
<text x="950" y="295" font-size="12" fill="#f57c00" text-anchor="end">app memory</text>
</svg>