
<svg width="980" height="750" viewBox="0 0 980 750" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="980" height="750" fill="#FFFFFF"/>
  <!-- Device <PERSON> -->
  <rect x="20" y="20" width="940" height="640" fill="none" stroke="#28a745" stroke-width="3" stroke-dasharray="10,5" rx="15"/>
  <text x="40" y="50" font-size="22" font-weight="bold" fill="#28a745">OFFLINE SECURITY BOUNDARY</text>
  <!-- Application Interface -->
  <rect x="70" y="70" width="840" height="80" fill="#61dafb" fill-opacity="0.2" stroke="#0056b3" stroke-width="2" rx="10"/>
  <text x="490" y="100" text-anchor="middle" font-size="20" font-weight="bold">Application Interface</text>
  <text x="490" y="125" text-anchor="middle" font-size="18">Chat Interface • Model Management • Performance Monitoring</text>
  <!-- LLM Inference Engine -->
  <rect x="70" y="180" width="360" height="200" fill="#ff6b6b" fill-opacity="0.15" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="260" y="210" text-anchor="middle" font-size="20" font-weight="bold">LLM Inference Engine</text>
  <!-- LLM Components -->
  <rect x="90" y="300" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="165" y="327" text-anchor="middle" font-size="18" font-weight="bold">LLaMA.rn</text>
  <text x="165" y="343" text-anchor="middle" font-size="16">GGUF Models</text>
  <rect x="260" y="300" width="150" height="60" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="335" y="327" text-anchor="middle" font-size="18" font-weight="bold">Context Engine</text>
  <text x="335" y="343" text-anchor="middle" font-size="16">RAG Integration</text>
  <rect x="90" y="230" width="320" height="60" fill="#ff6b6b" fill-opacity="0.25" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="250" y="255" text-anchor="middle" font-size="18" font-weight="bold">Generation Pipeline</text>
  <text x="250" y="275" text-anchor="middle" font-size="16">Token Streaming • Performance Metrics</text>
  <!-- RAG Processing Pipeline -->
  <rect x="530" y="180" width="380" height="200" fill="#4ecdc4" fill-opacity="0.15" stroke="#17a2b8" stroke-width="2" rx="10"/>
  <text x="720" y="210" text-anchor="middle" font-size="20" font-weight="bold">RAG Processing Pipeline</text>
  <!-- RAG Components -->
  <rect x="550" y="300" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="600" y="327" text-anchor="middle" font-size="18" font-weight="bold">ONNX</text>
  <text x="600" y="343" text-anchor="middle" font-size="16">Embeddings</text>
  <rect x="670" y="300" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="327" text-anchor="middle" font-size="18" font-weight="bold">Tokenizer</text>
  <text x="720" y="343" text-anchor="middle" font-size="16">BERT-style</text>
  <rect x="790" y="300" width="100" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="840" y="327" text-anchor="middle" font-size="18" font-weight="bold">HNSW</text>
  <text x="840" y="343" text-anchor="middle" font-size="16">Vector Search</text>
  <rect x="550" y="230" width="340" height="60" fill="#4ecdc4" fill-opacity="0.25" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="720" y="255" text-anchor="middle" font-size="18" font-weight="bold">Document Retrieval</text>
  <text x="720" y="275" text-anchor="middle" font-size="16">Chunked Metadata • LRU Cache • Ranking</text>
  <!-- Storage Layer -->
  <rect x="70" y="410" width="610" height="80" fill="#ffd93d" fill-opacity="0.2" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="375" y="440" text-anchor="middle" font-size="20" font-weight="bold">Local Storage</text>
  <text x="375" y="465" text-anchor="middle" font-size="18">HNSW Index • Document Chunks • AI Models • Runtime Cache</text>
  <!-- Native Bridge -->
  <rect x="740" y="410" width="170" height="80" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="825" y="440" text-anchor="middle" font-size="20" font-weight="bold">Native Bridge</text>
  <text x="825" y="465" text-anchor="middle" font-size="18">Rust HNSW</text>
  <!-- Runtime Offline Divider -->
  <text x="40" y="515" font-size="20" font-weight="bold">DEVICE RUNTIME</text>
  <path d="M 35 525 L 945 525" stroke="#333" stroke-width="2" stroke-dasharray="5,10"/>
  
  <text x="40" y="550" font-size="20" font-weight="bold">BUILD MACHINE</text>
  <!-- Data Processing Pipeline -->
  <rect x="70" y="560" width="840" height="80" fill="#4CAF50" fill-opacity="0.2" stroke="#4CAF50" stroke-width="2" rx="10"/>
  <text x="490" y="590" text-anchor="middle" font-size="20" font-weight="bold">Data Processing Pipeline</text>
  <text x="490" y="615" text-anchor="middle" font-size="18">Ingestion • Chunking • Embedding • HNSW Indexing • Cross-platform Distribution</text>

  <!-- Data Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Main Data Flow -->
  <!-- Build Index-->
  <path d="M 490 560 L 490 490" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="500" y="550" font-size="18" font-weight="bold">1. Deploy to Device</text>
  <!-- User Input -->
  <path d="M 215 150 L 215 180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="90" y="170" font-size="18" font-weight="bold">2. User Query</text>
  <!-- Query to RAG -->
  <path d="M 430 260 L 530 260" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="440" y="245" font-size="18" font-weight="bold">3. Process</text>
  <!-- Context Back -->
  <path d="M 530 310 L 430 310" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="440" y="335" font-size="18" font-weight="bold">4. Context</text>
  <!-- Storage Access -->
  <path d="M 600 380 L 600 410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 250 380 L 250 410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 825 380 L 825 410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 740 450 L 680 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Response Flow -->
  <path d="M 305 180 L 305 150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="320" y="170" font-size="18" font-weight="bold">5. Response</text>
  <!-- Key Benefits -->
  <rect x="415" y="680" width="150" height="50" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="5"/>
  <text x="490" y="710" text-anchor="middle" font-size="20" font-weight="bold">Internet</text>
</svg>